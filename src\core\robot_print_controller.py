# src/core/robot_print_controller.py

import sys
import os
import time
import re
from typing import List, Tuple, Optional

# --- 项目路径设置 ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# --- 导入 ---
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    exit()


class RobotPrintController:
    """机械臂3D打印控制器 - 实现初始化、定位和G代码执行的核心逻辑层"""

    def __init__(self, robot_interface: RobotInterface):
        """
        初始化打印控制器
        
        Args:
            robot_interface: 机械臂硬件接口实例
        """
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd
        
        # 打印就绪位置定义 (相对于原点)
        self.print_ready_position = {
            'x': 0.0,      # X坐标：与原点相同
            'y': 0.0,      # Y坐标：与原点相同  
            'z': -30.0,    # Z坐标：原点下方30mm
            'rx': 0.0,     # 绕X轴旋转角度
            'ry': 0.0,     # 绕Y轴旋转角度
            'rz': -90.0    # 绕Z轴旋转角度 (末端执行器垂直向下)
        }
        
        # 当前位置缓存
        self.current_position = None
        self.print_ready_pos_absolute = None

    def robot_initialize_and_power_on(self) -> bool:
        """
        健壮的机器人初始化和上电流程
        按照INEXBOT要求：先设置伺服就绪，再上电启用运动控制
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        print("\n--- 开始机器人初始化和上电流程 ---")
        try:
            # 步骤1: 清除错误
            print("步骤 1: 清除错误...")
            nrc.clear_error(self.socket_fd)
            time.sleep(0.5)

            # 步骤2: 获取当前伺服状态
            print("步骤 2: 获取当前伺服状态...")
            result = nrc.get_servo_state(self.socket_fd, 0)
            
            if isinstance(result, list) and len(result) > 1:
                ret_code = result[0]
                current_state = result[1]
            else:
                print(f"错误: 获取伺服状态返回格式不正确: {result}")
                return False

            if ret_code != 0:
                print(f"错误: 获取伺服状态API调用失败，返回码: {ret_code}")
                return False
                
            print(f"  -> 当前伺服状态为 {current_state}")

            # 步骤3: 根据状态设置伺服就绪态(1)
            if current_state == 0:  # 如果是关闭状态，设置为就绪
                print("  -> 设置伺服为就绪状态...")
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)
            elif current_state == 3:  # 如果是运行状态，先下电再设置就绪
                print("  -> 当前为运行状态，先下电...")
                nrc.set_servo_poweroff(self.socket_fd)
                time.sleep(1)
                print("  -> 设置伺服为就绪状态...")
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)

            # 步骤4: 执行上电操作
            print("步骤 4: 执行上电操作...")
            ret_code = nrc.set_servo_poweron(self.socket_fd)
            if ret_code != 0:
                print(f"上电失败！返回码: {ret_code}。请检查安全回路/急停/远程模式。")
                return False

            time.sleep(1)
            
            # 步骤5: 确认最终状态
            result = nrc.get_servo_state(self.socket_fd, 0)
            final_state = result[1] if isinstance(result, list) and len(result) > 1 else -1

            if final_state == 3:
                print(f"✅ 成功！机器人已上电，进入运行状态 (3)。")
                return True
            else:
                print(f"错误: 上电后状态异常，当前: {final_state}")
                return False

        except Exception as e:
            print(f"初始化和上电流程中发生异常: {e}")
            return False

    def get_current_cartesian_position(self) -> Optional[List[float]]:
        """
        获取当前笛卡尔坐标位置
        
        Returns:
            List[float]: 当前位置 [X, Y, Z, RX, RY, RZ] 或 None
        """
        try:
            cart_pos_container = nrc.VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos_container)
            if ret_code == 0:
                position = [cart_pos_container[i] for i in range(len(cart_pos_container))]
                self.current_position = position
                return position
            else:
                print(f"获取笛卡尔位置失败，返回码: {ret_code}")
                return None
        except Exception as e:
            print(f"获取笛卡尔位置时发生异常: {e}")
            return None

    def move_to_home_position(self) -> bool:
        """
        将机械臂移动到原点/零点位置
        
        Returns:
            bool: 移动成功返回True，失败返回False
        """
        print("\n--- 开始回零点操作 ---")
        try:
            # 使用机器人接口的回零点方法
            ret_code = self.robot.robot_go_home()
            if ret_code == 0:
                print("✅ 机械臂已成功回到零点位置")
                time.sleep(2)  # 等待运动完成
                
                # 更新当前位置
                self.get_current_cartesian_position()
                return True
            else:
                print(f"❌ 回零点失败，返回码: {ret_code}")
                return False
                
        except Exception as e:
            print(f"回零点操作中发生异常: {e}")
            return False

    def move_to_print_ready_position(self) -> bool:
        """
        将机械臂移动到打印就绪位置
        打印就绪位置定义为：X,Y与原点相同，Z下降30mm，末端执行器垂直向下
        
        Returns:
            bool: 移动成功返回True，失败返回False
        """
        print("\n--- 开始移动到打印就绪位置 ---")
        
        # 获取当前位置作为基准
        current_pos = self.get_current_cartesian_position()
        if not current_pos:
            print("❌ 无法获取当前位置，无法计算打印就绪位置")
            return False
        
        # 计算打印就绪位置的绝对坐标
        print_ready_pos = [
            current_pos[0] + self.print_ready_position['x'],  # X
            current_pos[1] + self.print_ready_position['y'],  # Y  
            current_pos[2] + self.print_ready_position['z'],  # Z (下降30mm)
            self.print_ready_position['rx'],                  # RX
            self.print_ready_position['ry'],                  # RY
            self.print_ready_position['rz']                   # RZ (垂直向下)
        ]
        
        # 保存打印就绪位置的绝对坐标，用于后续G代码相对定位
        self.print_ready_pos_absolute = print_ready_pos.copy()
        
        print(f"目标打印就绪位置: X={print_ready_pos[0]:.3f}, Y={print_ready_pos[1]:.3f}, Z={print_ready_pos[2]:.3f}")
        print(f"                 RX={print_ready_pos[3]:.3f}, RY={print_ready_pos[4]:.3f}, RZ={print_ready_pos[5]:.3f}")
        
        try:
            # 执行直线运动到打印就绪位置
            ret_code = self.robot.robot_movel(print_ready_pos, vel=50, acc=30, dec=30)
            if ret_code == 0:
                print("✅ 机械臂已成功移动到打印就绪位置")
                time.sleep(3)  # 等待运动完成
                
                # 验证位置
                final_pos = self.get_current_cartesian_position()
                if final_pos:
                    print(f"实际到达位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")
                    print(f"               RX={final_pos[3]:.3f}, RY={final_pos[4]:.3f}, RZ={final_pos[5]:.3f}")
                
                return True
            else:
                print(f"❌ 移动到打印就绪位置失败，返回码: {ret_code}")
                return False
                
        except Exception as e:
            print(f"移动到打印就绪位置时发生异常: {e}")
            return False

    def parse_gcode_line(self, line: str) -> Optional[dict]:
        """
        解析单行G代码，提取坐标和参数
        
        Args:
            line: G代码行
            
        Returns:
            dict: 解析后的G代码参数，如果不是运动指令则返回None
        """
        line = line.strip()
        
        # 跳过注释和空行
        if not line or line.startswith(';'):
            return None
            
        # 只处理G0和G1运动指令
        if not (line.startswith('G0') or line.startswith('G1')):
            return None
            
        # 解析参数
        params = {}
        
        # 提取G指令类型
        if line.startswith('G0'):
            params['command'] = 'G0'
        elif line.startswith('G1'):
            params['command'] = 'G1'
            
        # 使用正则表达式提取坐标参数
        coord_pattern = r'([XYZABCEF])([-+]?\d*\.?\d+)'
        matches = re.findall(coord_pattern, line)
        
        for axis, value in matches:
            params[axis] = float(value)
            
        return params if len(params) > 1 else None

    def execute_gcode_from_file(self, gcode_file_path: str) -> bool:
        """
        从文件读取并执行G代码
        第一个点相对于打印就绪位置，后续点按G代码绝对坐标执行
        
        Args:
            gcode_file_path: G代码文件路径
            
        Returns:
            bool: 执行成功返回True，失败返回False
        """
        print(f"\n--- 开始执行G代码文件: {gcode_file_path} ---")
        
        if not self.print_ready_pos_absolute:
            print("❌ 错误：尚未设置打印就绪位置，请先执行初始化序列")
            return False
            
        try:
            with open(gcode_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print(f"读取到 {len(lines)} 行G代码")
            
            first_move = True
            executed_moves = 0
            
            for line_num, line in enumerate(lines, 1):
                gcode_params = self.parse_gcode_line(line)
                
                if not gcode_params:
                    continue  # 跳过非运动指令
                    
                # 构建目标位置
                target_pos = list(self.print_ready_pos_absolute)  # 从打印就绪位置开始
                
                if first_move:
                    # 第一个点：相对于打印就绪位置
                    print(f"执行第一个G代码点 (相对于打印就绪位置)...")
                    if 'X' in gcode_params:
                        target_pos[0] = self.print_ready_pos_absolute[0] + gcode_params['X']
                    if 'Y' in gcode_params:
                        target_pos[1] = self.print_ready_pos_absolute[1] + gcode_params['Y']
                    if 'Z' in gcode_params:
                        target_pos[2] = self.print_ready_pos_absolute[2] + gcode_params['Z']
                    first_move = False
                else:
                    # 后续点：使用G代码的绝对坐标
                    if 'X' in gcode_params:
                        target_pos[0] = gcode_params['X']
                    if 'Y' in gcode_params:
                        target_pos[1] = gcode_params['Y']
                    if 'Z' in gcode_params:
                        target_pos[2] = gcode_params['Z']
                
                # 处理旋转角度 (ABC对应ZYX欧拉角)
                if 'A' in gcode_params:  # A对应RZ
                    target_pos[5] = gcode_params['A']
                if 'B' in gcode_params:  # B对应RY
                    target_pos[4] = gcode_params['B']
                if 'C' in gcode_params:  # C对应RX
                    target_pos[3] = gcode_params['C']
                
                # 执行运动
                print(f"行 {line_num}: {gcode_params['command']} -> X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
                
                # 根据G代码类型选择运动速度
                if gcode_params['command'] == 'G0':
                    # G0快速移动
                    ret_code = self.robot.robot_movel(target_pos, vel=100, acc=50, dec=50)
                else:
                    # G1工作进给
                    ret_code = self.robot.robot_movel(target_pos, vel=30, acc=30, dec=30)
                
                if ret_code != 0:
                    print(f"❌ 执行G代码行 {line_num} 失败，返回码: {ret_code}")
                    return False
                    
                executed_moves += 1
                time.sleep(0.5)  # 短暂等待确保运动完成
                
                # 每执行10个动作暂停一下，避免过快
                if executed_moves % 10 == 0:
                    print(f"已执行 {executed_moves} 个运动指令...")
                    time.sleep(1)
            
            print(f"✅ G代码执行完成！总共执行了 {executed_moves} 个运动指令")
            return True
            
        except FileNotFoundError:
            print(f"❌ 错误：找不到G代码文件 {gcode_file_path}")
            return False
        except Exception as e:
            print(f"❌ 执行G代码时发生异常: {e}")
            return False

    def robot_power_off(self):
        """机器人安全下电"""
        print("\n--- 开始机器人下电流程 ---")
        try:
            nrc.set_servo_poweroff(self.socket_fd)
            time.sleep(1)
            print("✅ 机器人已安全下电。")
        except Exception as e:
            print(f"下电流程中发生异常: {e}")

    def execute_full_print_sequence(self, gcode_file_path: str) -> bool:
        """
        执行完整的3D打印序列：初始化 -> 回零 -> 打印就绪位置 -> 执行G代码
        
        Args:
            gcode_file_path: G代码文件路径
            
        Returns:
            bool: 整个序列执行成功返回True，失败返回False
        """
        print("=" * 60)
        print("           机械臂3D打印初始化和定位序列")
        print("=" * 60)
        
        # 步骤1: 初始化并上电
        if not self.robot_initialize_and_power_on():
            print("\n❌ 机器人初始化失败，打印序列中止")
            return False
        
        # 步骤2: 回零点
        if not self.move_to_home_position():
            print("\n❌ 回零点失败，打印序列中止")
            return False
            
        # 步骤3: 移动到打印就绪位置
        if not self.move_to_print_ready_position():
            print("\n❌ 移动到打印就绪位置失败，打印序列中止")
            return False
            
        # 步骤4: 执行G代码
        if not self.execute_gcode_from_file(gcode_file_path):
            print("\n❌ G代码执行失败，打印序列中止")
            return False
            
        print("\n🎉 完整的3D打印序列执行成功！")
        return True
