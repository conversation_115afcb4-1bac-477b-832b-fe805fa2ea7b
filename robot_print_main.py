# robot_print_main.py

import sys
import os
import time

# --- 项目路径设置 ---
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# --- 导入 ---
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
from src.core.robot_print_controller import RobotPrintController

try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    exit()


def main():
    """
    主函数：演示机械臂3D打印初始化和定位序列
    """
    robot = None
    print("=" * 70)
    print("           INEXBOT 机械臂 3D打印 初始化和定位序列")
    print("=" * 70)
    print("\n⚠️  警告：请确保机器人周围有足够的安全空间！")
    print("⚠️  确认急停按钮可用，安全回路正常！")
    print("⚠️  打印序列即将开始...\n")
    
    # 给用户5秒时间确认安全
    for i in range(5, 0, -1):
        print(f"倒计时 {i} 秒...")
        time.sleep(1)
    print("开始执行！\n")

    try:
        # 步骤1: 连接机器人
        print("步骤 1: 连接机械臂...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        print("✅ 机械臂连接成功")
        
        # 步骤2: 创建打印控制器
        print("\n步骤 2: 初始化打印控制器...")
        print_controller = RobotPrintController(robot)
        print("✅ 打印控制器初始化完成")
        
        # 步骤3: 执行完整的打印序列
        print("\n步骤 3: 执行完整的3D打印初始化序列...")
        gcode_file = "jiyi.Gcode"  # G代码文件路径
        
        success = print_controller.execute_full_print_sequence(gcode_file)
        
        if success:
            print("\n🎉 恭喜！机械臂3D打印序列执行成功！")
            print("✅ 机械臂已完成以下操作：")
            print("   1. 初始化和上电")
            print("   2. 回到零点位置")
            print("   3. 移动到打印就绪位置 (Z=-30mm, 末端垂直向下)")
            print("   4. 执行G代码打印路径")
        else:
            print("\n❌ 打印序列执行失败，请检查错误信息")

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生意外错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 步骤4: 安全下电并断开连接
        if robot:
            try:
                print("\n步骤 4: 安全下电和断开连接...")
                
                # 检查连接状态
                if nrc.get_connection_status(robot.socket_fd) == 0:
                    # 安全下电
                    print_controller.robot_power_off()
                
                # 断开连接
                robot.disconnect()
                print("✅ 机械臂已安全断开连接")
                
            except Exception as e:
                print(f"下电和断开连接时发生异常: {e}")
        
        print("\n程序执行结束。")


def test_initialization_only():
    """
    测试函数：仅执行初始化和定位，不执行G代码
    用于验证机械臂的基本功能
    """
    robot = None
    print("=" * 70)
    print("           机械臂初始化和定位测试 (不执行G代码)")
    print("=" * 70)
    
    try:
        # 连接机器人
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        print_controller = RobotPrintController(robot)
        
        # 仅执行初始化和定位
        print("\n--- 测试序列开始 ---")
        
        # 1. 初始化并上电
        if not print_controller.robot_initialize_and_power_on():
            print("❌ 初始化失败")
            return
            
        # 2. 回零点
        if not print_controller.move_to_home_position():
            print("❌ 回零点失败")
            return
            
        # 3. 移动到打印就绪位置
        if not print_controller.move_to_print_ready_position():
            print("❌ 移动到打印就绪位置失败")
            return
            
        print("\n✅ 初始化和定位测试成功完成！")
        print("机械臂现在位于打印就绪位置：")
        print("- X, Y: 与零点相同")
        print("- Z: 零点下方30mm")
        print("- 末端执行器: 垂直向下")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        if robot:
            try:
                print_controller.robot_power_off()
                robot.disconnect()
            except:
                pass


def interactive_menu():
    """
    交互式菜单，让用户选择执行模式
    """
    print("=" * 50)
    print("    INEXBOT 机械臂 3D打印控制程序")
    print("=" * 50)
    print("\n请选择执行模式：")
    print("1. 完整3D打印序列 (初始化 + 定位 + G代码执行)")
    print("2. 仅初始化和定位测试 (不执行G代码)")
    print("3. 退出程序")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                main()
                break
            elif choice == '2':
                test_initialization_only()
                break
            elif choice == '3':
                print("程序退出。")
                break
            else:
                print("无效选择，请输入 1、2 或 3")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断。")
            break
        except Exception as e:
            print(f"输入处理错误: {e}")


if __name__ == "__main__":
    # 检查G代码文件是否存在
    gcode_file = "jiyi.Gcode"
    if not os.path.exists(gcode_file):
        print(f"⚠️  警告：G代码文件 '{gcode_file}' 不存在")
        print("程序仍可运行初始化和定位测试")
    
    # 启动交互式菜单
    interactive_menu()
