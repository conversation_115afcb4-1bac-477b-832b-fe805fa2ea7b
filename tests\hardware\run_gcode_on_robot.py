#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用于在物理机器人上执行G-code路径的脚本。(版本 8 - 最终兼容版)

此版本为兼容旧版 Python SDK 而设计，仅使用已确认可用的函数。

核心功能:
1. 【兼容模式】使用手动定义的笛卡尔坐标 HOME_POSITION 和 movel 指令进行归位。
2. 【兼容模式】使用固定的 time.sleep() 等待动作完成。
3. 执行前移动到G-code路径起点的上方“准备点”。
4. 路径结束后抬起Z轴并返回归位点。
"""

import sys
import os
import time
import re
import numpy as np

# --- 项目路径设置 ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
try:
    import nrc_interface as nrc
    print("成功导入 nrc_interface 模块 (兼容模式)")
except ImportError as e:
    print(f"错误: 导入 nrc_interface 失败: {e}")
    exit()

# =============================================================================
# --- 【【【核心配置区域】】】 ---
# =============================================================================

# 1. G-code文件路径
SIMULATION_FILES_DIR = os.path.join(PROJECT_ROOT, 'src', 'simulation')
GCODE_FILE_PATH = os.path.join(SIMULATION_FILES_DIR, "jiyi.Gcode")

# 2. 【【【重要！！！请在此处配置一个可达的归位点】】】
#    由于 robot_home 不可用，我们必须手动定义一个归位点。
#    请务必通过示教器，将机器人移动到一个安全的、可达的位置，然后记录其笛卡尔坐标。
#    建议让工具头垂直朝下（B轴约等于90度），这样姿态更稳定。
#    这是一个示例，请务必用您自己的示教值替换！
HOME_POSITION = [350.0, 0.0, 500.0, 0.0, 90.0, 0.0]  # 示例值 [X, Y, Z, A, B, C]

# 3. 打印前的安全高度偏移
PRINT_READY_Z_OFFSET = 30.0

# 4. 坐标系变换配置
TRANSLATION_VECTOR = [600.0, 0.0, 300.0]
ROTATION_MATRIX = np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])

# 5. 运动参数
MOTION_VELOCITY = 80.0
FAST_VELOCITY = 150.0
MOTION_ACC = 50.0
MOTION_DEC = 50.0
MOTION_PL = 2
WAIT_TIME_AFTER_FAST_MOVE_SEC = 2.5  # 归位等长距离移动，需要更长的等待时间
WAIT_TIME_AFTER_PRINT_MOVE_SEC = 0.3 # G-code点之间移动，等待时间可以短

# =============================================================================

# (后续代码已适配兼容模式)
TRANSFORMATION_MATRIX = np.identity(4)
TRANSFORMATION_MATRIX[0:3, 0:3] = ROTATION_MATRIX
TRANSFORMATION_MATRIX[0:3, 3] = TRANSLATION_VECTOR

def parse_gcode_file(file_path):
    print(f"\n--- 正在解析G-code文件: {file_path} ---")
    try:
        with open(file_path, 'r', encoding='utf-8') as f: gcode_content = f.readlines()
    except FileNotFoundError: print(f"错误: G-code文件未找到: {file_path}"); return None
    path_points = []
    current_x,current_y,current_z,default_a,default_b,default_c,current_a,current_b,current_c=0,0,0,0.0,0.0,0.0,0.0,0.0,0.0
    for line in gcode_content:
        line=line.strip()
        if line.startswith(';') or not line: continue
        if line.startswith('G0') or line.startswith('G1'):
            m=re.search(r'X([-+]?\d*\.?\d+)',line),re.search(r'Y([-+]?\d*\.?\d+)',line),re.search(r'Z([-+]?\d*\.?\d+)',line),re.search(r'A([-+]?\d*\.?\d+)',line),re.search(r'B([-+]?\d*\.?\d+)',line),re.search(r'C([-+]?\d*\.?\d+)',line)
            if m[0]:current_x=float(m[0].group(1))
            if m[1]:current_y=float(m[1].group(1))
            if m[2]:current_z=float(m[2].group(1))
            if any(m[3:]):
                if m[3]:current_a=float(m[3].group(1))
                if m[4]:current_b=float(m[4].group(1))
                if m[5]:current_c=float(m[5].group(1))
            else:current_a,current_b,current_c=default_a,default_b,default_c
            path_points.append([current_x,current_y,current_z,'E' in line and 'E-' not in line,current_a,current_b,current_c,0])
    print(f"✅ G-code解析完成，共获得 {len(path_points)} 个路径点。")
    return path_points

def gcode_to_robot_coords(gcode_point):
    x_local,y_local,z_local,_,a_deg,b_deg,c_deg,_=gcode_point
    local_point_vec=np.array([x_local,y_local,z_local,1])
    robot_point_vec=TRANSFORMATION_MATRIX @ local_point_vec
    return [robot_point_vec[0],robot_point_vec[1],robot_point_vec[2],a_deg,b_deg,c_deg]


class GcodeExecutor:
    def __init__(self, robot_interface: RobotInterface, gcode_file_path: str):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd
        self.gcode_path_local = parse_gcode_file(gcode_file_path)
        if not self.gcode_path_local: raise ValueError("G-code文件加载失败")

    def robot_initialize_and_power_on(self):
        print("\n--- 开始机器人初始化和上电流程 ---")
        try:
            nrc.clear_error(self.socket_fd); time.sleep(0.5)
            result=nrc.get_servo_state(self.socket_fd,0)
            if not(isinstance(result,list) and len(result)>1):return False
            ret_code,current_state=result
            if ret_code!=0:return False
            print(f"  -> 当前伺服状态为 {current_state}")
            if current_state==0:nrc.set_servo_state(self.socket_fd,1);time.sleep(0.5)
            elif current_state==3:nrc.set_servo_poweroff(self.socket_fd);time.sleep(1)
            ret_code=nrc.set_servo_poweron(self.socket_fd)
            if ret_code!=0:print(f"上电失败！返回码: {ret_code}。");return False
            time.sleep(1)
            result=nrc.get_servo_state(self.socket_fd,0)
            final_state=result[1] if isinstance(result,list) and len(result)>1 else -1
            if final_state==3:print("✅ 成功！机器人已上电，进入运行状态 (3)。");return True
            else:print(f"错误: 上电后状态异常，当前: {final_state}");return False
        except Exception as e:print(f"初始化和上电流程中发生异常: {e}");return False

    def move_to_cartesian_pos(self, target_pos, velocity, description, wait_time):
        """直线移动到笛卡尔目标点，并使用固定延时等待"""
        print(f"\n--- {description} ---")
        print(f"正在直线移动至 -> {[f'{p:.2f}' for p in target_pos]}")
        move_cmd = nrc.MoveCmd(); move_cmd.velocity=velocity; move_cmd.acc=MOTION_ACC; move_cmd.dec=MOTION_DEC; move_cmd.pl=MOTION_PL; move_cmd.coord=1; move_cmd.targetPosValue=nrc.VectorDouble(target_pos)
        ret_code = nrc.robot_movel(self.socket_fd, move_cmd)
        if ret_code != 0:
            print(f"  ❌ 错误: 移动指令发送失败，返回码: {ret_code}。")
            nrc.clear_error(self.socket_fd)
            return False
        print(f"  -> 指令已发送，等待 {wait_time} 秒...");time.sleep(wait_time);print("✅ 位置到达。");return True

    def run_gcode_path(self):
        first_gcode_point = self.gcode_path_local[0]
        first_robot_coords = gcode_to_robot_coords(first_gcode_point)
        print_ready_position = list(first_robot_coords)
        print_ready_position[2] += PRINT_READY_Z_OFFSET
        if not self.move_to_cartesian_pos(print_ready_position, FAST_VELOCITY, "移动到打印准备点", WAIT_TIME_AFTER_FAST_MOVE_SEC): return False

        print("\n--- 开始执行G-code打印序列 ---")
        move_cmd = nrc.MoveCmd(); move_cmd.velocity = MOTION_VELOCITY; move_cmd.acc = MOTION_ACC; move_cmd.dec = MOTION_DEC; move_cmd.pl = MOTION_PL; move_cmd.coord = 1
        total_points = len(self.gcode_path_local)
        for i, point in enumerate(self.gcode_path_local):
            target_robot_coords = gcode_to_robot_coords(point)
            print(f"({i+1}/{total_points}) G-code移动 -> {[f'{p:.2f}' for p in target_robot_coords]}")
            move_cmd.targetPosValue = nrc.VectorDouble(target_robot_coords)
            ret_code = nrc.robot_movel(self.socket_fd, move_cmd)
            if ret_code != 0: print(f"  ❌ 错误: G-code移动失败，返回码: {ret_code}。"); return False
            time.sleep(WAIT_TIME_AFTER_PRINT_MOVE_SEC)
        print("\n🎉 G-code路径全部执行完毕！")

        print("\n--- 执行收尾动作 ---")
        last_robot_coords = gcode_to_robot_coords(self.gcode_path_local[-1])
        retract_position = list(last_robot_coords)
        retract_position[2] += PRINT_READY_Z_OFFSET
        if not self.move_to_cartesian_pos(retract_position, FAST_VELOCITY, "抬起工具头", WAIT_TIME_AFTER_FAST_MOVE_SEC): return False
        return True

    def robot_power_off(self):
        print("\n--- 开始机器人下电流程 ---")
        try:nrc.set_servo_poweroff(self.socket_fd);time.sleep(1);print("✅ 机器人已安全下电。")
        except Exception as e:print(f"下电流程中发生异常: {e}")

def main():
    robot, executor = None, None
    print("="*60+"\n        机 器 人 G - C O D E 路径执行程序 (V8-兼容模式)\n"+"="*60)
    if not os.path.exists(GCODE_FILE_PATH): print(f"\n❌ 错误：G-code文件不存在: {GCODE_FILE_PATH}"); return
    print("\n⚠️  警告：请确保机器人周围有足够的安全空间！\n将在 5 秒后开始连接并初始化机器人...")
    time.sleep(5)
    try:
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        executor = GcodeExecutor(robot, GCODE_FILE_PATH)
        if not executor.robot_initialize_and_power_on(): print("\n❌ 机器人未能成功上电"); return
        
        # --- 兼容模式下的执行流程 ---
        # 1. 使用直线移动到手动定义的归位点
        if not executor.move_to_cartesian_pos(HOME_POSITION, FAST_VELOCITY, "移动到手动归位点", WAIT_TIME_AFTER_FAST_MOVE_SEC):
            print("\n❌ 移动到归位点失败，请检查HOME_POSITION设置！"); return
        
        # 2. 执行打印任务
        if not executor.run_gcode_path(): print("\n❌ 打印任务执行失败"); return
        
        # 3. 任务结束，再次返回归位点
        if not executor.move_to_cartesian_pos(HOME_POSITION, FAST_VELOCITY, "任务完成，返回归位点", WAIT_TIME_AFTER_FAST_MOVE_SEC):
            print("\n❌ 任务后归位失败"); return

    except ValueError as e: print(f"\n❌ 初始化失败: {e}")
    except Exception as e: print(f"\n❌ 测试过程中发生意外错误: {e}")
    finally:
        if robot and executor: executor.robot_power_off()
        if robot: robot.disconnect()
        print("\n测试结束。")

if __name__ == "__main__":
    main()