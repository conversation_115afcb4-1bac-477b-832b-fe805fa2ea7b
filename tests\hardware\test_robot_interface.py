# tests/hardware/test_robot_interface.py

import pytest
import sys
import os

# 将项目根目录添加到Python路径中，以便能够找到 src, lib, config.py
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)

# 将 lib 目录也明确添加到路径中
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface

def test_robot_connection():
    """
    测试机器人的连接和断开功能。
    这是一个集成测试，因为它需要实际的硬件连接。
    """
    robot = None
    try:
        print(f"尝试连接到机器人: {ROBOT_IP}:{ROBOT_PORT}")

        # 1. 初始化并连接
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 2. 验证连接状态
        status = robot.get_connection_status()
        print(f"获取到的连接状态码: {status}")
        assert status == 0, "连接状态码不为0，表示查询失败"

        print("机器人连接和状态检查测试成功！")

    except RuntimeError as e:
        print(f"连接失败（这在没有实际硬件时是正常的）: {e}")
        print("测试框架工作正常，但需要实际的机器人硬件才能完成连接测试。")

    except Exception as e:
        print(f"发生意外错误: {e}")
        raise

    finally:
        # 3. 确保无论测试是否成功，都执行断开操作
        if robot:
            robot.disconnect()

def test_robot_interface_import():
    """
    测试能否正确导入机器人接口模块
    """
    try:
        from src.hardware.robot_interface import RobotInterface
        print("✅ 机器人接口模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 机器人接口模块导入失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("机器人接口测试")
    print("=" * 50)

    # 强制刷新输出缓冲区
    import sys
    sys.stdout.flush()

    # 测试模块导入
    print("\n1. 测试模块导入...")
    sys.stdout.flush()
    import_success = test_robot_interface_import()
    sys.stdout.flush()

    if import_success:
        print("\n2. 测试机器人连接...")
        sys.stdout.flush()
        test_robot_connection()
        sys.stdout.flush()

    print("\n测试完成！")
    sys.stdout.flush()
