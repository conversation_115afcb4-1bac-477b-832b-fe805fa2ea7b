# src/hardware/robot_interface.py

import sys
import os

# 将 lib 目录的绝对路径添加到系统路径中
LIB_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../lib'))
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# 现在可以安全地导入 nrc_interface 了
import nrc_interface as nrc

class RobotInterface:
    """封装与INEXBOT机械臂通信的接口类"""

    def __init__(self, ip: str, port: int):
        """
        初始化并连接机器人。
        如果连接失败，则抛出 RuntimeError 异常。
        
        Args:
            ip (str): 机器人控制器的IP地址
            port (int): 机器人控制器的端口号
        """
        print(f"正在尝试连接机器人 {ip}:{port}...")
        self.socket_fd = nrc.connect_robot(ip, str(port))

        if self.socket_fd < 0:
            raise RuntimeError(f"连接机器人失败，错误码: {self.socket_fd}")

        print(f"机器人连接成功，套接字描述符: {self.socket_fd}")

    def disconnect(self):
        """安全地断开与机器人的连接"""
        if hasattr(self, 'socket_fd') and self.socket_fd >= 0:
            print("正在断开机器人连接...")
            nrc.disconnect_robot(self.socket_fd)
            print("机器人连接已断开。")

    def get_connection_status(self):
        """
        检查当前连接状态

        Returns:
            int: 连接状态码，0表示连接正常
        """
        if hasattr(self, 'socket_fd') and self.socket_fd >= 0:
            return nrc.get_connection_status(self.socket_fd)
        return -1

    def get_servo_state(self):
        """
        获取伺服状态

        Returns:
            int: 伺服状态码
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        servo_status = 0
        result = nrc.get_servo_state(self.socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            return result[1]  # 根据重要补充.md，从列表第二个元素获取数据
        return result

    def get_robot_running_state(self):
        """
        获取机器人运行状态

        Returns:
            int: 运行状态码
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        running_status = 0
        result = nrc.get_robot_running_state(self.socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            return result[1]
        return result

    def get_current_position(self, coord_type=0):
        """
        获取当前位置

        Args:
            coord_type (int): 坐标系类型，0=关节坐标系，1=笛卡尔坐标系

        Returns:
            tuple: (返回码, 位置数据列表)
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1, []

        pos = nrc.VectorDouble()
        result = nrc.get_current_position(self.socket_fd, coord_type, pos)

        # 将VectorDouble转换为Python列表
        position_list = []
        try:
            for i in range(len(pos)):
                position_list.append(pos[i])
        except:
            pass

        return result, position_list

    def get_robot_type(self):
        """
        获取机器人类型

        Returns:
            int: 机器人类型码
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        robot_type = 0
        result = nrc.get_robot_type(self.socket_fd, robot_type)
        if isinstance(result, list) and len(result) > 1:
            return result[1]
        return result

    def get_current_coord(self):
        """
        获取当前坐标系

        Returns:
            int: 当前坐标系类型
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        coord = 0
        result = nrc.get_current_coord(self.socket_fd, coord)
        if isinstance(result, list) and len(result) > 1:
            return result[1]
        return result

    # ===== 运动控制方法 =====
    # 基于测试结果，运动指令是可以工作的，即使状态查询有问题

    def robot_go_home(self):
        """
        机器人回零点

        Returns:
            int: 执行结果，0表示成功
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        try:
            result = nrc.robot_go_home_robot(self.socket_fd, 0)  # 0 = robotNum
            return result
        except Exception as e:
            print(f"回零点失败: {e}")
            return -1

    def robot_go_to_reset_position(self):
        """
        机器人回复位点

        Returns:
            int: 执行结果，0表示成功
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        try:
            result = nrc.robot_go_to_reset_position_robot(self.socket_fd, 0)
            return result
        except Exception as e:
            print(f"回复位点失败: {e}")
            return -1

    def robot_movej(self, joint_angles, vel=50, acc=50, dec=50):
        """
        关节运动

        Args:
            joint_angles (list): 目标关节角度列表
            vel (int): 速度百分比 (1-100)
            acc (int): 加速度百分比 (1-100)
            dec (int): 减速度百分比 (1-100)

        Returns:
            int: 执行结果，0表示成功
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        try:
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 0  # 关节坐标

            # 设置目标关节位置
            target_joints = nrc.VectorDouble()
            for angle in joint_angles:
                target_joints.append(float(angle))
            move_cmd.targetPosValue = target_joints

            # 设置运动参数
            move_cmd.vel = vel
            move_cmd.acc = acc
            move_cmd.dec = dec
            move_cmd.coord = 0  # 关节坐标系
            move_cmd.blendR = 0.0
            move_cmd.blendType = 0

            result = nrc.robot_movej_robot(self.socket_fd, 0, move_cmd)
            return result
        except Exception as e:
            print(f"关节运动失败: {e}")
            return -1

    def robot_movel(self, cartesian_pos, vel=100, acc=50, dec=50):
        """
        直线运动

        Args:
            cartesian_pos (list): 目标笛卡尔位置 [X, Y, Z, RX, RY, RZ]
            vel (int): 线速度 (mm/s)
            acc (int): 加速度百分比 (1-100)
            dec (int): 减速度百分比 (1-100)

        Returns:
            int: 执行结果，0表示成功
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        try:
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 笛卡尔坐标

            # 设置目标位置
            target_pos = nrc.VectorDouble()
            for pos in cartesian_pos:
                target_pos.append(float(pos))
            move_cmd.targetPosValue = target_pos

            # 设置运动参数
            move_cmd.vel = vel
            move_cmd.acc = acc
            move_cmd.dec = dec
            move_cmd.coord = 1  # 笛卡尔坐标系
            move_cmd.blendR = 0.0
            move_cmd.blendType = 0

            result = nrc.robot_movel_robot(self.socket_fd, 0, move_cmd)
            return result
        except Exception as e:
            print(f"直线运动失败: {e}")
            return -1

    def clear_error(self):
        """
        清除机器人错误

        Returns:
            int: 执行结果，0表示成功
        """
        if not hasattr(self, 'socket_fd') or self.socket_fd < 0:
            return -1

        try:
            result = nrc.clear_error_robot(self.socket_fd, 0)
            return result
        except Exception as e:
            print(f"清除错误失败: {e}")
            return -1
